import type { Row } from '@tanstack/react-table';
import { useMutation } from 'convex/react';
import {
  CircleCheckBigIcon,
  MoreHorizontal,
  PenToolIcon,
  StarIcon,
  TrashIcon,
  UndoIcon,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import AddGroupToArticle from './add-group';
import type { TArticle } from './article-table';
import CreateOrUpdateArticle from './create-edit-article';

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}
export default function ArticleTableColumnActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const article = row.original as TArticle;
  const deleteArticle = useMutation(api.articles.deleteArticle);
  const restoreArticle = useMutation(api.articles.restoreArticle);
  const toggleFavoriteArticle = useMutation(api.articles.toggleFavoriteArticle);
  const stageArticle = useMutation(api.articles.stageArticle);
  const unStageArticle = useMutation(api.articles.unStageArticle);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  if (!article || 'success' in article) {
    return null;
  }
  const handleDeleteArticle = () => {
    try {
      deleteArticle({ id: article._id });
      toast.success('Article deleted successfully!');
    } catch {
      toast.error('Failed to delete article.');
    }
  };
  const handleRestoreArticle = () => {
    try {
      restoreArticle({ id: article._id });
      toast.success('Article restored successfully!');
    } catch {
      toast.error('Failed to restore article.');
    }
  };
  const handleToggleFavoriteArticle = () => {
    try {
      toggleFavoriteArticle({ id: article._id });
      if (article.isFavorite) {
        toast.success('Article removed from favorite!');
      } else {
        toast.success('Article added to favorite!');
      }
    } catch {
      toast.error('Failed to toggle favorite article.');
    }
  };
  const handleStageArticle = () => {
    try {
      stageArticle({ id: article._id });
      toast.success('Article staged successfully!');
    } catch {
      toast.error('Failed to stage article.');
    }
  };
  const handleUnStageArticle = () => {
    try {
      unStageArticle({ id: article._id });
      toast.success('Article unstaged successfully!');
    } catch {
      toast.error('Failed to unstage article.');
    }
  };
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            className="flex h-8 w-8 cursor-pointer p-0 data-[state=open]:bg-muted"
            variant="ghost"
          >
            <MoreHorizontal className="size-4" />
            <span className="sr-only">Open staff actions</span>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-56 px-2 py-4">
          {article.isFavorite ? (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleToggleFavoriteArticle}
            >
              <span className="text-sm">Remove from favorite</span>
              <StarIcon className="size-4 fill-primary text-muted-foreground" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleToggleFavoriteArticle}
            >
              <span className="text-sm">Add to favorite</span>
              <StarIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          )}
          {article.status === 'staged' ? (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleUnStageArticle}
            >
              <span className="text-sm">Unstage</span>
              <UndoIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleStageArticle}
            >
              <span className="text-sm">Stage</span>
              <CircleCheckBigIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() => setOpenEditDialog(true)}
          >
            <span className="text-sm">Edit</span>
            <PenToolIcon className="size-4 text-muted-foreground" />
          </DropdownMenuItem>
          {article.status === 'deleted' ? (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleRestoreArticle}
            >
              <span className="text-sm">Restore</span>
              <UndoIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleDeleteArticle}
            >
              <span className="text-sm">Delete</span>
              <TrashIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          )}
          <AddGroupToArticle
            articleId={article._id}
            prevGroupId={article.groupId as Id<'groups'>}
          />
        </DropdownMenuContent>
      </DropdownMenu>
      <CreateOrUpdateArticle
        description={article.description}
        id={article._id}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
        title={article.title}
      />
    </>
  );
}
