import type { Row } from '@tanstack/react-table';
import { useMutation } from 'convex/react';

import { MoreHorizontal, PenToolIcon, TrashIcon, UndoIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { api } from '@/convex/_generated/api';
import CreateOrUpdateGroup from './create-update-group';
import type { TGroup } from './group-card';
export default function GroupTableColumnActions<TData>({
  row,
}: {
  row: Row<TData>;
}) {
  const group = row.original as TGroup;
  const deleteGroup = useMutation(api.groups.deleteGroup);
  const restoreGroup = useMutation(api.groups.restoreGroup);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  if (!group || 'success' in group) {
    return null;
  }
  const handleDeleteGroup = () => {
    try {
      deleteGroup({ groupId: group._id });
      toast.success('Group deleted successfully!');
    } catch {
      toast.error('Failed to delete group.');
    }
  };
  const handleRestoreGroup = () => {
    try {
      restoreGroup({ groupId: group._id });
      toast.success('Group restored successfully!');
    } catch {
      toast.error('Failed to restore group.');
    }
  };
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            className="flex h-8 w-8 cursor-pointer p-0 data-[state=open]:bg-muted"
            variant="ghost"
          >
            <MoreHorizontal className="size-4" />
            <span className="sr-only">Open group actions</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56 px-2 py-4">
          <DropdownMenuItem
            className="flex justify-between text-muted-foreground hover:text-primary"
            onClick={() => setOpenEditDialog(true)}
          >
            Edit
            <PenToolIcon className="size-4" />
          </DropdownMenuItem>
          {group.status === 'approved' ? (
            <DropdownMenuItem
              className="flex justify-between text-muted-foreground hover:text-primary"
              onClick={handleDeleteGroup}
            >
              Delete
              <TrashIcon className="size-4" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="flex justify-between text-muted-foreground hover:text-primary"
              onClick={handleRestoreGroup}
            >
              Restore
              <UndoIcon className="size-4" />
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <CreateOrUpdateGroup
        description={group.description}
        id={group._id}
        name={group.name}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
      />
    </>
  );
}
