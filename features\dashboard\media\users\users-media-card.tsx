import {
  CopyIcon,
  DownloadIcon,
  ShareIcon,
  ShieldAlertIcon,
} from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';
import type { TMedia } from '@/features/dashboard/media/types';
export default function UsersMediaCard({ media }: { media: TMedia }) {
  if (!media || 'success' in media) {
    return null;
  }
  const handleCopyUrl = () => {
    try {
      navigator.clipboard.writeText(media.url);
      // wait 1 sec to show toast
      setTimeout(() => {
        toast.success('URL copied to clipboard!');
      }, 1000);
    } catch {
      toast.error('Failed to copy URL');
    }
  };
  const handleDownload = () => {
    try {
      const link = document.createElement('a');
      link.href = media.url;
      link.download = media.title;
      link.click();
      toast.success('Download started!');
    } catch {
      toast.error('Failed to download');
    }
  };
  return (
    <ContextMenu key={media._id}>
      <ContextMenuTrigger asChild className="relative">
        <button
          className="w-full cursor-pointer"
          onClick={handleCopyUrl}
          type="button"
        >
          <AspectRatio className="rounded-lg bg-muted" ratio={16 / 9}>
            <Image
              alt="Photo by Drew Beamer"
              className="h-full w-full rounded-lg object-cover ring ring-muted"
              fill
              src={media.url}
            />
          </AspectRatio>
        </button>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem
          className="flex cursor-pointer items-center justify-between"
          onClick={handleCopyUrl}
        >
          Copy Url
          <CopyIcon className="size-4" />
        </ContextMenuItem>
        <ContextMenuItem
          className="flex cursor-pointer items-center justify-between"
          onClick={handleDownload}
        >
          Download
          <DownloadIcon className="size-4" />
        </ContextMenuItem>
        <ContextMenuItem className="flex cursor-pointer items-center justify-between">
          Share
          <ShareIcon className="size-4" />
        </ContextMenuItem>
        <ContextMenuItem className="flex cursor-pointer items-center justify-between">
          Report
          <ShieldAlertIcon className="size-4" />
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}
