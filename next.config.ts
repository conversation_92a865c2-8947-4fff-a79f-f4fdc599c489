import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      new URL("https://lh3.googleusercontent.com/a/**"),
      new URL("https://modest-anaconda-155.convex.cloud/api/storage/**"),
      new URL("https://3u39ha98bi.ufs.sh/f/**"),
    {
      protocol: "https",
      hostname: "better-flow.ed7d0f33fb5c1628368d2013a92a2f55.r2.cloudflarestorage.com",
      // port: "",
      // pathname: "/a/**",
    }
    ],
  },
};

export default nextConfig;
