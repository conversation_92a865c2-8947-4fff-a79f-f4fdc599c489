import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { preloadQuery } from 'convex/nextjs';
import { api } from '@/convex/_generated/api';
import EmailCard from '@/features/dashboard/settings/components/auth/email-card';
import { PhoneCard } from '@/features/dashboard/settings/components/auth/phone-card';
import TwoFactorAuth from '@/features/dashboard/settings/components/auth/two-factor';

export default async function AuthenticationPage() {
  const preloadedUser = await preloadQuery(
    api.users.getUser,
    {},
    { token: await convexAuthNextjsToken() }
  );

  if (!preloadedUser) {
    return null;
  }
  return (
    <main className="flex h-full w-full flex-col gap-6">
      <EmailCard preloadedUser={preloadedUser} />
      <PhoneCard preloadedUser={preloadedUser} />
      <TwoFactorAuth preloadedUser={preloadedUser} />
    </main>
  );
}
