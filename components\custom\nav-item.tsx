import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import { useState } from 'react';
import { cn } from '@/lib/utils';
export default function NavItem({
  id,
  item,
  active,
}: {
  id: number;
  item: { label: string; link: string };
  active: boolean;
}) {
  const [hovered, setHovered] = useState<number | null>(null);
  return (
    <motion.div
      className={cn(
        'flex h-11 items-center border-b-2',
        active ? 'border-primary' : 'border-transparent'
      )}
      onMouseLeave={() => setHovered(null)}
    >
      <Link
        className={cn(
          'relative h-8 px-4 py-2 text-sm hover:text-accent-foreground',
          active ? 'text-primary' : 'text-primary/80'
        )}
        href={item.link}
        key={`link-${id}`}
        onMouseEnter={() => setHovered(id)}
      >
        <AnimatePresence>
          {hovered === id && (
            <motion.span
              animate={{
                opacity: 1,
                transition: { duration: 0.15 },
              }}
              className="absolute inset-0 block h-full w-full rounded-sm bg-accent dark:bg-accent/50"
              exit={{
                opacity: 0,
                transition: { duration: 0.15, delay: 1 },
              }}
              initial={{ opacity: 0 }}
              layoutId="hovered"
            />
          )}
        </AnimatePresence>
        <span className="relative z-20">{item.label}</span>
      </Link>
    </motion.div>
  );
}
