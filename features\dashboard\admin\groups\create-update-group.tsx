'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { cn } from '@/lib/utils';
import { groupSchema, type TGroupSchema } from './schema';

interface CreateOrUpdateGroupProps {
  openDialog?: boolean;
  setOpenDialog?: (open: boolean) => void;
  name?: string;
  description?: string;
  id?: Id<'groups'>;
}

export default function CreateOrUpdateGroup({
  openDialog,
  setOpenDialog,
  name,
  description,
  id,
}: CreateOrUpdateGroupProps) {
  const isEditMode = Boolean(id);
  const createGroupMutation = useMutation(api.groups.createGroup);
  const updateGroupMutation = useMutation(api.groups.updateGroup);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [internalOpen, setInternalOpen] = useState(false);

  const open = openDialog ?? internalOpen;
  const setOpen = setOpenDialog ?? setInternalOpen;

  const form = useForm<TGroupSchema>({
    resolver: zodResolver(groupSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });
  useEffect(() => {
    if (open) {
      form.reset({
        name: name || '',
        description: description || '',
      });
    }
  }, [open, name, description, form]);

  const handleSubmit = async (data: TGroupSchema) => {
    setIsSubmitting(true);
    setOpen(false);

    try {
      if (isEditMode) {
        if (!id) {
          toast.error('Failed to get group id.');
          return;
        }
        const result = await updateGroupMutation({
          id,
          ...data,
        });
        if (result?.success) {
          form.reset();
          toast.success('Group updated successfully!');
        } else {
          toast.error(result.error);
        }
      } else {
        const result = await createGroupMutation(data);
        if (result?.success) {
          form.reset();
          toast.success('Group created successfully!');
        } else {
          toast.error(result.error);
        }
      }
    } catch {
      toast.error('Something went wrong.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog onOpenChange={setOpen} open={open}>
      {!isEditMode && (
        <DialogTrigger
          className={cn(
            buttonVariants({ variant: 'default', size: 'lg' }),
            'cursor-pointer rounded-sm px-3'
          )}
        >
          Add New...
        </DialogTrigger>
      )}

      <DialogContent className="flex w-full max-w-md flex-col">
        <DialogHeader className="sr-only">
          <DialogTitle>
            {isEditMode ? 'Update' : 'Create New'} Group
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            className="mt-6 space-y-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Input placeholder="Description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button className="w-full" disabled={isSubmitting} type="submit">
              {isSubmitting ? (
                <Spinner text="Submitting..." />
              ) : isEditMode ? (
                'Update Group'
              ) : (
                'Create Group'
              )}
            </Button>
          </form>
        </Form>
        <DialogClose />
      </DialogContent>
    </Dialog>
  );
}
