import { DataTable } from '@/components/custom/data-table';
import type { TGroup } from './group-card';
import { groupColumns } from './group-column';

export default function GroupTable({ groups }: { groups: TGroup[] }) {
  if (!(groups && Array.isArray(groups))) {
    return null;
  }
  // Handle empty groups state
  if (groups.length === 0) {
    return (
      <div className="p-4 text-muted-foreground">
        No groups found. Create your first group!
      </div>
    );
  }

  return (
    <div className=" rounded-md bg-background p-4">
      <DataTable columns={groupColumns} data={groups} />
    </div>
  );
}
