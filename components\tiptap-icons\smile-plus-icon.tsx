import * as React from "react"

export const SmilePlusIcon = React.memo(
  ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
    return (
      <svg
        width="24"
        height="24"
        className={className}
        viewBox="0 0 24 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.67909 4.05505C9.27254 3.18292 11.0926 2.81337 12.9 2.995C13.4495 3.05022 13.9398 2.64952 13.995 2.1C14.0502 1.55048 13.6495 1.06024 13.1 1.00502C10.8909 0.783033 8.66641 1.2347 6.71886 2.30064C4.7713 3.36659 3.19189 4.99691 2.18824 6.97729C1.1846 8.95767 0.803706 11.1954 1.09564 13.3963C1.38758 15.5972 2.33867 17.6583 3.8239 19.3085C5.30912 20.9588 7.25894 22.121 9.41704 22.6423C11.5751 23.1637 13.8405 23.0198 15.9153 22.2296C17.9901 21.4394 19.7772 20.0398 21.0417 18.2149C22.3062 16.39 22.9889 14.2252 23 12.005L23 12V11C23 10.4477 22.5523 10 22 10C21.4477 10 21 10.4477 21 11V11.9976C20.9904 13.8132 20.4319 15.5834 19.3978 17.0758C18.3632 18.5689 16.901 19.714 15.2034 20.3605C13.5059 21.0071 11.6524 21.1248 9.8867 20.6982C8.12098 20.2717 6.52567 19.3208 5.31048 17.9706C4.0953 16.6204 3.31713 14.9341 3.07828 13.1333C2.83942 11.3326 3.15106 9.50171 3.97222 7.8814C4.79339 6.26109 6.08564 4.92719 7.67909 4.05505ZM7.4 13.2C7.84043 12.8697 8.46468 12.9576 8.79686 13.3958L8.8015 13.4018C8.80745 13.4093 8.81876 13.4234 8.83531 13.4431C8.86846 13.4826 8.92219 13.5442 8.99542 13.6206C9.14272 13.7743 9.36407 13.9828 9.65067 14.1913C10.2277 14.6109 11.0255 15 12 15C12.9745 15 13.7723 14.6109 14.3493 14.1913C14.6359 13.9828 14.8573 13.7743 15.0046 13.6206C15.0778 13.5442 15.1315 13.4826 15.1647 13.4431C15.1812 13.4234 15.1925 13.4093 15.1985 13.4018L15.2031 13.3958C15.5353 12.9576 16.1596 12.8697 16.6 13.2C17.0418 13.5314 17.1314 14.1582 16.8 14.6L16 14C16.8 14.6 16.7998 14.6003 16.7995 14.6006L16.799 14.6013L16.7978 14.6029L16.7951 14.6065L16.7879 14.6159L16.7666 14.6432C16.7493 14.6649 16.726 14.6938 16.6966 14.7288C16.638 14.7987 16.555 14.8933 16.4485 15.0044C16.2365 15.2257 15.9266 15.5172 15.5257 15.8087C14.7277 16.3891 13.5255 17 12 17C10.4745 17 9.27227 16.3891 8.47433 15.8087C8.07343 15.5172 7.76352 15.2257 7.55145 15.0044C7.445 14.8933 7.362 14.7987 7.30336 14.7288C7.274 14.6938 7.25065 14.6649 7.23341 14.6432L7.21211 14.6159L7.2049 14.6065L7.20216 14.6029L7.20101 14.6013L7.20048 14.6006C7.20024 14.6003 7.2 14.6 8 14L7.2 14.6C6.86863 14.1582 6.95817 13.5314 7.4 13.2ZM8 9C8 8.44772 8.44772 8 9 8H9.01C9.56229 8 10.01 8.44772 10.01 9C10.01 9.55229 9.56229 10 9.01 10H9C8.44772 10 8 9.55229 8 9ZM15 8C14.4477 8 14 8.44772 14 9C14 9.55229 14.4477 10 15 10H15.01C15.5623 10 16.01 9.55229 16.01 9C16.01 8.44772 15.5623 8 15.01 8H15ZM15 5C15 4.44772 15.4477 4 16 4H18V2C18 1.44772 18.4477 1 19 1C19.5523 1 20 1.44772 20 2V4H22C22.5523 4 23 4.44772 23 5C23 5.55228 22.5523 6 22 6H20V8C20 8.55228 19.5523 9 19 9C18.4477 9 18 8.55228 18 8V6H16C15.4477 6 15 5.55228 15 5Z"
          fill="currentColor"
        />
      </svg>
    )
  }
)

SmilePlusIcon.displayName = "SmilePlusIcon"
