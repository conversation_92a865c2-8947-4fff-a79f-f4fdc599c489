import { v } from "convex/values";
import { api } from "./_generated/api";
import { mutation, query, QueryCtx } from "./_generated/server";
import { requireUser } from "./users";
import { Id } from "./_generated/dataModel";
import { r2 } from "./media";

// ---- Constants ----
const ROLES = {
  ADMIN: "admin",
} as const;

const ARTICLE_STATUS = {
  DRAFT: "draft",
  STAGED: "staged",
  APPROVED: "approved",
  PUBLISHED: "published",
} as const;

// ---- Helpers ----
async function requireAdmin(ctx: any) {
  const userId = await requireUser(ctx);
  const user = await ctx.db.get(userId);
  if (!user) return { success: false, error: "User not found." };
  if (user.role !== ROLES.ADMIN)
    return { success: false, error: "Unauthorized." };
  return { success: true, user };
}

async function getArticleOrFail(ctx: QueryCtx, id: Id<"articles">) {
  const article = await ctx.db.get(id);
  if (!article) return { success: false, error: "Article not found." };
  return { success: true, article };
}

export const getArticlesByAdmin = query({
  args: {},
  handler: async (ctx) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;

      const articles = await ctx.db
        .query("articles")
        .filter((q) => q.neq(q.field("status"), ARTICLE_STATUS.DRAFT))
        .collect();
      return { success: true, data: articles };
    } catch {
      return { success: false, error: "Failed to fetch articles." };
    }
  },
});

export const getMediaFilesByAdmin = query({
  args: {},
  handler: async (ctx) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;

      const images = await ctx.db
        .query("mediaFiles")
        .filter((q) => q.neq(q.field("status"), ARTICLE_STATUS.DRAFT))
        .collect();
      const metadata = await Promise.all(
        images.map(async (image) => {
          const metadata = await r2.getMetadata(ctx, image.key);
          // i want size and contentType
          return {
            size: metadata ? metadata.size : 0,
            contentType: metadata ? metadata.contentType : "image/jpeg",
          };
        })
      );
      const data = await Promise.all(
        images.map(async (image) => ({
          ...image,
          size: metadata[images.indexOf(image)].size,
          contentType: metadata[images.indexOf(image)].contentType,
          url: await r2.getUrl(image.key),
        }))
      );
      return { success: true, data: data };
    } catch {
      return { success: false, error: "Failed to fetch images." };
    }
  },
});

// ---- Article Management ----
export const approveArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;

      const articleCheck = await getArticleOrFail(ctx, args.id);
      if (!articleCheck.success) return articleCheck;
      const article = articleCheck.article;
      if (!article) return { success: false, error: "Article not found." };

      await ctx.db.patch(article._id, {
        status: ARTICLE_STATUS.APPROVED,
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "approved" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to approve article." };
    }
  },
});
export const rejectArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;

      const articleCheck = await getArticleOrFail(ctx, args.id);
      if (!articleCheck.success) return articleCheck;
      const article = articleCheck.article;
      if (!article) return { success: false, error: "Article not found." };

      await ctx.db.patch(article._id, {
        status: "draft",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "rejected" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to reject article." };
    }
  },
});
export const unapproveArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;

      const articleCheck = await getArticleOrFail(ctx, args.id);
      if (!articleCheck.success) return articleCheck;
      const article = articleCheck.article;
      if (!article) return { success: false, error: "Article not found." };

      await ctx.db.patch(article._id, {
        status: "staged",
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "unapproved" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to unapprove article." };
    }
  },
});
export const publishArticle = mutation({
  args: {
    id: v.id("articles"),
  },
  handler: async (ctx, args) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;

      const articleCheck = await getArticleOrFail(ctx, args.id);
      if (!articleCheck.success) return articleCheck;
      const article = articleCheck.article;
      if (!article) return { success: false, error: "Article not found." };

      await ctx.db.patch(article._id, {
        status: "published",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: args.id,
        },
        docTitle: article.title,
        docStatus: article.status,
        action: "published" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to publish article." };
    }
  },
});

// ----- Images Management -----
export const approveMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;
      const image = await ctx.db.get(args.id);
      if (!image) return { success: false, error: "Image not found." };

      await ctx.db.patch(image._id, {
        status: "approved",
        updatedAt: Date.now(),
      });

      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: "approved" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to approve image." };
    }
  },
});
export const rejectMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;
      const image = await ctx.db.get(args.id);
      if (!image) return { success: false, error: "Image not found." };
      await ctx.db.patch(image._id, {
        status: "draft",
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: "rejected" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to reject image." };
    }
  },
});
export const unapproveMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;
      const image = await ctx.db.get(args.id);
      if (!image) return { success: false, error: "Image not found." };
      await ctx.db.patch(image._id, {
        status: "staged",
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: "unapproved" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to unapprove image." };
    }
  },
});
export const publishMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const adminCheck = await requireAdmin(ctx);
      if (!adminCheck.success) return adminCheck;
      const image = await ctx.db.get(args.id);
      if (!image) return { success: false, error: "Image not found." };
      await ctx.db.patch(image._id, {
        status: "published",
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: "published" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to publish image." };
    }
  },
});

// --- Analytics ---
export const getArticlesAnalytics = query({
  args: {},
  handler: async (ctx) => {
    // const adminCheck = await requireAdmin(ctx);
    // if (!adminCheck.success) return adminCheck;

    const articles = await ctx.db
      .query("articles")
      .filter((q) => q.neq(q.field("status"), ARTICLE_STATUS.DRAFT))
      .collect();

    return articles.map((article) => ({
      date: new Date(article.updatedAt || article._creationTime)
        .toISOString()
        .split("T")[0], // 'YYYY-MM-DD'
      status: article.status,
    }));
  },
});

export const getMediaAnalytics = query({
  args: {},
  handler: async (ctx) => {
    // const adminCheck = await requireAdmin(ctx);
    // if (!adminCheck.success) return adminCheck;
    const media = await ctx.db
      .query("mediaFiles")
      .filter((q) => q.neq(q.field("status"), ARTICLE_STATUS.DRAFT))
      .collect();
    return media.map((item) => ({
      date: new Date(item._creationTime).toISOString().split("T")[0], // 'YYYY-MM-DD'
      status: item.status,
    }));
  },
});

export const getTotalArticlesByStatus = query({
  args: {
    status: v.union(
      v.literal("draft"),
      v.literal("staged"),
      v.literal("approved"),
      v.literal("published"),
      v.literal("deleted")
    ),
  },
  handler: async (ctx, { status }): Promise<number> => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return 0;
    const total = await ctx.db
      .query("articles")
      .filter((q) => q.eq(q.field("status"), status))
      .collect()
      .then((articles) => articles.length);

    return total;
  },
});

// export const getTotalMediaByStatus = query({
//   args: {
//     status: v.union(
//       v.literal("draft"),
//       v.literal("staged"),
//       v.literal("approved"),
//       v.literal("published"),
//       v.literal("deleted")
//     ),
//   },
//   handler: async (ctx, { status }): Promise<number> => {
//     const adminCheck = await requireAdmin(ctx);
//     if (!adminCheck.success) return 0;
//     const total = await ctx.db
//       .query("mediaDocs")
//       .withIndex("by_status", (q) => q.eq("status", status))
//       .collect()
//       .then((media) => media.length);
//     // return number only
//     return total;
//   },
// });
export const getTotalMediaFilesByStatus = query({
  args: {
    status: v.union(
      v.literal("draft"),
      v.literal("staged"),
      v.literal("approved"),
      v.literal("published"),
      v.literal("deleted")
    ),
  },
  handler: async (ctx, { status }): Promise<number> => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return 0;
    const total = await ctx.db
      .query("mediaFiles")
      .withIndex("by_status", (q) => q.eq("status", status))
      .collect()
      .then((images) => images.length);
    return total;
  },
});

export const getTotalUsers = query({
  args: {},
  handler: async (ctx) => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return 0;
    const total = await ctx.db
      .query("users")
      .collect()
      .then((users) => users.length);
    return total;
  },
});

export const getTotalUsersByRole = query({
  args: {
    role: v.union(
      v.literal("author"),
      v.literal("admin"),
      v.literal("media-manager"),
      v.literal("ads-manager")
    ),
  },
  handler: async (ctx, { role }): Promise<number> => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return 0;
    const total = await ctx.db
      .query("users")
      .withIndex("by_role", (q) => q.eq("role", role))
      .collect()
      .then((users) => users.length);
    return total;
  },
});

export const getTotalVerifiedUsers = query({
  args: {},
  handler: async (ctx) => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return 0;
    const total = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("verified"), true))
      .collect()
      .then((users) => users.length);
    return total;
  },
});

// ---- Staff Management ----
export const getStaffMembers = query({
  args: {},
  handler: async (ctx) => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return [];
    const users = await ctx.db
      .query("users")
      .filter((q) => q.neq(q.field("role"), "admin"))
      .collect();
    return users;
  },
});

export const getStaffMember = query({
  args: {
    id: v.id("users"),
  },
  handler: async (ctx, args) => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return null;
    const user = await ctx.db.get(args.id);
    return user;
  },
});

export const verifyStaffMember = mutation({
  args: {
    id: v.id("users"),
  },
  handler: async (ctx, args) => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return adminCheck;
    await ctx.db.patch(args.id, { verified: true });
    return { success: true };
  },
});

export const unverifyStaffMember = mutation({
  args: {
    id: v.id("users"),
  },
  handler: async (ctx, args) => {
    const adminCheck = await requireAdmin(ctx);
    if (!adminCheck.success) return adminCheck;
    await ctx.db.patch(args.id, { verified: false });
    return { success: true };
  },
});

export const searchStaffMembers = query({
  args: {
    query: v.string(),
  },
  handler: async (ctx, { query }) => {
    try {
      const users = await ctx.db
        .query("users")
        .withSearchIndex("search_username", (q) => q.search("username", query))
        .collect();
      return users;
    } catch {
      return { success: false, error: "Failed to search users." };
    }
  },
});
