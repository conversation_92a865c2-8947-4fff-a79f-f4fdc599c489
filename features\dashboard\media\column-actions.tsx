import type { Row } from '@tanstack/react-table';
import { useMutation } from 'convex/react';
import {
  CircleCheckBigIcon,
  MoreHorizontal,
  PenToolIcon,
  StarIcon,
  TrashIcon,
  UndoIcon,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import AddGroupToMedia from './add-group';
import UpdateMedia from './create-edit-media';
import type { TMedia } from './types';

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}
export default function MediaTableColumnActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const media = row.original as TMedia;
  const deleteMedia = useMutation(api.media.deleteMediaFileTemp);
  const restoreMedia = useMutation(api.media.restoreMediaFile);
  const toggleFavoriteMedia = useMutation(api.media.toggleFavoriteMediaFile);
  const stageMedia = useMutation(api.media.stageMediaFile);
  const unStageMedia = useMutation(api.media.unStageMediaFile);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  if (!media || 'success' in media) {
    return null;
  }
  const handleDeleteMedia = () => {
    try {
      deleteMedia({ id: media._id });
      toast.success('Media deleted successfully!');
    } catch {
      toast.error('Failed to delete media.');
    }
  };
  const handleRestoreMedia = () => {
    try {
      restoreMedia({ id: media._id });
      toast.success('Media restored successfully!');
    } catch {
      toast.error('Failed to restore media.');
    }
  };
  const handleToggleFavoriteMedia = () => {
    try {
      toggleFavoriteMedia({ id: media._id });
      if (media.isFavorite) {
        toast.success('Media removed from favorite!');
      } else {
        toast.success('Media   added to favorite!');
      }
    } catch {
      toast.error('Failed to toggle favorite media.');
    }
  };
  const handleStageMedia = () => {
    try {
      stageMedia({ id: media._id });
      toast.success('Media staged successfully!');
    } catch {
      toast.error('Failed to stage media.');
    }
  };
  const handleUnStageMedia = () => {
    try {
      unStageMedia({ id: media._id });
      toast.success('Media unstaged successfully!');
    } catch {
      toast.error('Failed to unstage media.');
    }
  };
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            className="flex h-8 w-8 cursor-pointer p-0 data-[state=open]:bg-muted"
            variant="ghost"
          >
            <MoreHorizontal className="size-4" />
            <span className="sr-only">Open staff actions</span>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-56 px-2 py-4">
          {media.isFavorite ? (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleToggleFavoriteMedia}
            >
              <span className="text-sm">Remove from favorite</span>
              <StarIcon className="size-4 fill-primary text-muted-foreground" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleToggleFavoriteMedia}
            >
              <span className="text-sm">Add to favorite</span>
              <StarIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          )}
          {media.status === 'staged' ? (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleUnStageMedia}
            >
              <span className="text-sm">Unstage</span>
              <UndoIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleStageMedia}
            >
              <span className="text-sm">Stage</span>
              <CircleCheckBigIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() => setOpenEditDialog(true)}
          >
            <span className="text-sm">Edit</span>
            <PenToolIcon className="size-4 text-muted-foreground" />
          </DropdownMenuItem>
          {media.status === 'deleted' ? (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleRestoreMedia}
            >
              <span className="text-sm">Restore</span>
              <UndoIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="flex w-full cursor-pointer items-center justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={handleDeleteMedia}
            >
              <span className="text-sm">Delete</span>
              <TrashIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          )}
          <AddGroupToMedia
            mediaId={media._id}
            prevGroupId={media.groupId as Id<'groups'>}
          />
        </DropdownMenuContent>
      </DropdownMenu>
      <UpdateMedia
        id={media._id}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
        title={media.title}
        url={media.url}
      />
    </>
  );
}
