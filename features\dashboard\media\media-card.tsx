import { formatDistanceToNow } from 'date-fns';
import { DatabaseIcon, FileImageIcon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';
import { formatBytes } from '@/hooks/use-file-upload';
import { cn } from '@/lib/utils';
import { getIconByStatus } from '../shared/icons';
import { MediaCardActions } from './media-card-actions';
import type { TMedia } from './types';

export default function MediaCard({ media }: { media: TMedia }) {
  if (!media || 'success' in media) {
    return null;
  }
  const Icon = getIconByStatus(media.status);
  return (
    <Card
      className={cn(
        'w-full rounded-sm shadow-xs ring-ring/20 hover:ring dark:bg-background dark:ring-ring/40',
        media.status === 'deleted' &&
          'ring-1 ring-[#f49ca2ca] dark:ring-[#551a1e]'
      )}
      key={media._id}
    >
      <CardHeader>
        <CardTitle className="mr-7 flex items-center gap-2 truncate">
          <FileImageIcon className="size-4 text-muted-foreground" />
          <span className="truncate">{media.title || 'Untitled Media'}</span>
        </CardTitle>
        <CardDescription className="mr-10 truncate">
          <HoverCard>
            <HoverCardTrigger asChild>
              <Link className="hover:underline" href="/">
                better-flow.com/{media.slug}
              </Link>
            </HoverCardTrigger>
            <HoverCardContent className="p-2">
              <AspectRatio className="rounded-lg bg-muted" ratio={16 / 9}>
                {media.url ? (
                  <Image
                    alt="Photo by Drew Beamer"
                    className="h-full w-full rounded-lg object-cover dark:brightness-[0.2] dark:grayscale"
                    fill
                    src={media.url}
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                    <p className="text-sm">No preview available</p>
                  </div>
                )}
              </AspectRatio>
            </HoverCardContent>
          </HoverCard>
        </CardDescription>
        <CardAction>
          <MediaCardActions media={media} />
        </CardAction>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <div className="flex items-center gap-1">
          <Icon className="size-4 text-muted-foreground" />
          <p className="font-medium text-sm capitalize tracking-tight">
            {media.status}
          </p>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1 text-muted-foreground">
            <DatabaseIcon className="size-4" />
            <p className=" text-sm capitalize tracking-tight">
              {formatBytes(media.size || 0)}
            </p>
          </div>

          <p className="text-muted-foreground text-sm">
            {formatDistanceToNow(new Date(media._creationTime), {
              addSuffix: true,
            })}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
