'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { formatDistanceToNow } from 'date-fns';
import { DatabaseIcon, StarIcon } from 'lucide-react';
import Link from 'next/link';
import { DataTableColumnHeader } from '@/components/custom/data-table-column-header';
import { DragHandle } from '@/components/custom/drag-handle';
import { formatBytes } from '@/hooks/use-file-upload';
import { capitalize, truncate } from '@/lib/utils';
import { getIconByStatus } from '../shared/icons';
import MediaTableColumnActions from './column-actions';
import type { TMedia } from './types';

export const MediaColumns: ColumnDef<TMedia>[] = [
  {
    id: 'dragHandle',
    header: '',
    cell: () => <DragHandle />,
    meta: {
      label: 'Drag Handle',
    },
  },
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => {
      const isFavorite =
        'isFavorite' in row.original && row.original.isFavorite === true;

      const articleName = (row.getValue('title') as string) ?? '';

      return (
        <Link
          className="flex items-center gap-4 font-medium decoration-dashed underline-offset-4 hover:underline"
          href={'/admin/articles'}
        >
          {truncate(capitalize(articleName), 40)}
          {isFavorite && <StarIcon className="size-4 text-muted-foreground" />}
        </Link>
      );
    },
    meta: {
      label: 'Title',
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      const Icon = getIconByStatus(status);

      return (
        <div className="flex items-center gap-1">
          <Icon className="size-4 text-muted-foreground" />
          <p className="font-medium text-sm capitalize tracking-tight">
            {status}
          </p>
        </div>
      );
    },
    meta: {
      label: 'Status',
    },
  },
  {
    accessorKey: 'contentType',
    header: 'Type',
    cell: ({ row }) => {
      const contentType = row.getValue('contentType') as string;
      return (
        <div className="flex items-center gap-1">
          <p className="font-normal text-muted-foreground text-sm capitalize tracking-tight">
            {contentType}
          </p>
        </div>
      );
    },
    meta: {
      label: 'Type',
    },
  },
  {
    accessorKey: 'size',
    header: 'Size',
    cell: ({ row }) => {
      const size = row.getValue('size') as number;
      return (
        <div className="flex items-center gap-1">
          <DatabaseIcon className="size-4 text-muted-foreground" />
          <p className="font-normal text-muted-foreground text-sm capitalize tracking-tight">
            {formatBytes(size || 0)}
          </p>
        </div>
      );
    },
    meta: {
      label: 'Size',
    },
  },
  {
    accessorKey: '_creationTime',
    header: 'Created At',
    cell: ({ row }) => {
      return (
        <span className="text-muted-foreground">
          {formatDistanceToNow(new Date(row.getValue('_creationTime')), {
            addSuffix: true,
          })}
        </span>
      );
    },
    meta: {
      label: 'Created At',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => <MediaTableColumnActions row={row} />,
    meta: {
      label: 'Actions',
    },
  },
];
