import { MessageCircle, PlusIcon, SearchIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export function ActivityTab() {
  return (
    <div className="h-full w-full">
      <div className="flex items-center justify-between gap-4">
        <div className="relative flex-1">
          <Input
            className="peer ps-9"
            placeholder="Search activity..."
            type="search"
          />
          <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
            <SearchIcon size={16} />
          </div>
        </div>
        <Button className="rounded-sm" variant="outline">
          <PlusIcon className="size-4" />
          Filter
        </Button>
      </div>
      <div className="flex h-full min-h-100 w-full flex-col items-center justify-center gap-2">
        <div className="flex items-center justify-center rounded-full bg-muted/50 p-2">
          <MessageCircle className="size-6 text-muted-foreground" />
        </div>
        <p className="text-muted-foreground text-sm">No new activity</p>
      </div>
    </div>
  );
}
