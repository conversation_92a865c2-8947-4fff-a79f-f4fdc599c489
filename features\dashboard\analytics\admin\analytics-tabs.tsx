import { TabsView } from '@/components/custom/tabs-view';
import AdminStaffAnalyticsCards from './admin-staff-cards';
import ArticleAnalyticsSection from './article-section';
import MediaAnalyticsSection from './media-section';

export default function AnalyticsTabs() {
  const tabs = [
    {
      value: 'articles',
      label: 'Articles',
      component: <ArticleAnalyticsSection />,
    },
    {
      value: 'media',
      label: 'Media',
      component: <MediaAnalyticsSection />,
    },
    {
      value: 'staff',
      label: 'Staff',
      component: <AdminStaffAnalyticsCards />,
    },
  ];

  return <TabsView defaultValue="articles" tabs={tabs} />;
}
