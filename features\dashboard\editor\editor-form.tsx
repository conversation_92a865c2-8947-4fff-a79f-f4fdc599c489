'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { type Preloaded, useMutation, usePreloadedQuery } from 'convex/react';
import { ChevronLeftIcon } from 'lucide-react';
import Link from 'next/link';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import TextareaAutosize from 'react-textarea-autosize';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
import { SimpleEditor } from '@/components/tiptap-templates/simple/simple-editor';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { api } from '@/convex/_generated/api';
import {
  articleEditorFormSchema,
  type TArticleEditorFormSchema,
} from './schema';

interface EditorProps {
  preloadedArticle: Preloaded<typeof api.articles.getArticle>;
}

export default function EditorForm({ preloadedArticle }: EditorProps) {
  const article = usePreloadedQuery(preloadedArticle);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const addContentArticle = useMutation(api.articles.updateArticleContent);
  const form = useForm<TArticleEditorFormSchema>({
    resolver: zodResolver(articleEditorFormSchema),
    defaultValues: {
      title: article && !('success' in article) ? article.title : '',
      description:
        article && !('success' in article) ? article.description || '' : '',
      content: article && !('success' in article) ? article.content || '' : '',
      words: article && !('success' in article) ? article.words || 0 : 0,
    },
  });
  const handleEditorChange = useCallback(
    (html: string, wordsEditor: number) => {
      form.setValue('content', html);
      form.setValue('words', wordsEditor);
    },
    [form]
  );

  if (!article || 'success' in article) {
    return null;
  }
  // const { _id, title, description, content, words, status } = article;

  // const form = useForm<TArticleEditorFormSchema>({
  //   resolver: zodResolver(articleEditorFormSchema),
  //   defaultValues: {
  //     title,
  //     description,
  //     content: content ?? '',
  //     words: words ?? 0,
  //   },
  // });
  const onSubmit = async (formData: TArticleEditorFormSchema) => {
    setIsSubmitting(true);
    try {
      const createPromise = addContentArticle({
        id: article._id,
        ...formData,
      });

      toast.promise(createPromise, {
        loading: 'Updating content...',
        success: 'Content updated successfully.',
        error: 'Failed to update the draft.',
      });
      const response = await createPromise;
      if (response?.success) {
        toast.success('Content updated successfully.');
      } else {
        toast.error(response.error as unknown as string);
      }
    } catch {
      toast.error('Failed to update the draft.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Memoize editor change handler for performance

  return (
    <Form {...form}>
      <form
        className="mx-auto mt-10 flex w-full max-w-6xl flex-col gap-10"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <div className="flex items-center justify-between">
          <Button asChild type="button" variant="ghost">
            <Link className="group" href="/journalist">
              <ChevronLeftIcon className="mr-2 size-4 transition-all duration-300 ease-in-out group-hover:translate-x-1" />
              Back
            </Link>
          </Button>
          <Button
            className="w-fit cursor-pointer justify-end"
            disabled={isSubmitting}
            type="submit"
          >
            {isSubmitting ? <Spinner text="Saving..." /> : 'Save'}
          </Button>
        </div>

        <div className="flex flex-col gap-4">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Title</FormLabel>
                <FormControl>
                  <TextareaAutosize
                    className="w-full resize-none appearance-none overflow-hidden bg-transparent font-bold text-3xl focus:outline-0"
                    disabled={isSubmitting}
                    placeholder="Untitled"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Description</FormLabel>
                <FormControl>
                  <TextareaAutosize
                    className="w-full resize-none appearance-none overflow-hidden bg-transparent text-base text-muted-foreground tracking-tight focus:outline-0"
                    disabled={isSubmitting}
                    placeholder="Type your descriptio here... (optional)"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">Content</FormLabel>
                <FormControl>
                  <SimpleEditor
                    content={field.value ?? ''}
                    disabled={isSubmitting}
                    editable={article.status !== 'deleted'}
                    id={article._id}
                    onChange={handleEditorChange}
                    placeholder="Start writing..."
                    words={article.words ?? 0}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
}
