'use client';
import { useQuery } from 'convex/react';
import { useEffect, useMemo, useState } from 'react';
import { api } from '@/convex/_generated/api';
import ViewToggle from '@/features/dashboard/shared/components/view-toggle';
import { SearchStatus } from '@/features/dashboard/shared/search/search-status';
import SearchTop from '@/features/dashboard/shared/search/search-top';
import ItemCard from './article-card';
import ArticleTable from './article-table';
import CreateOrUpdateArticle from './create-edit-article';

export default function ArticleCards({ isAdmin }: { isAdmin?: boolean }) {
  const [searchText, setSearchText] = useState('');
  const [view, setView] = useState<'grid' | 'table'>('grid');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([
    'draft',
    'staged',
    'approved',
    'published',
    'deleted',
  ]);
  const authorArticles = useQuery(api.articles.getArticles) || [];
  const adminArticles = useQuery(api.admin.getArticlesByAdmin) || [];

  const allArticles = isAdmin
    ? adminArticles && 'data' in adminArticles
      ? adminArticles.data
      : []
    : authorArticles;
  const searchResults =
    useQuery(api.articles.searchArticles, {
      query: searchText,
    }) || [];

  useEffect(() => {
    const storedView = localStorage.getItem('articleView');
    if (storedView === 'grid' || storedView === 'table') {
      setView(storedView);
    } else {
      localStorage.setItem('articleView', 'grid');
    }
  }, []);

  const filteredArticles = useMemo(() => {
    if (!(allArticles && Array.isArray(allArticles))) {
      return [];
    }

    return allArticles.filter((article) =>
      selectedStatuses.includes(article.status)
    );
  }, [allArticles, selectedStatuses]);

  const handleStatusChange = (statuses: string[]) => {
    setSelectedStatuses(statuses);
  };

  const articles = useMemo(() => {
    return searchText ? searchResults : filteredArticles;
  }, [searchText, searchResults, filteredArticles]);

  if (!(articles && Array.isArray(articles))) {
    return null;
  }

  return (
    <>
      <div className="flex items-center justify-between gap-2">
        <SearchTop
          searchPlaceholder="Search Articles..."
          searchText={searchText}
          setSearchText={setSearchText}
        />
        <SearchStatus
          onStatusChange={handleStatusChange}
          selectedStatuses={selectedStatuses}
        />
        <ViewToggle setView={setView} view={view} />
        <CreateOrUpdateArticle />
      </div>
      {view === 'grid' ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {articles.map((article) => (
            <ItemCard article={article} key={article._id} />
          ))}
          {articles.length === 0 && (
            <div className="col-span-full flex flex-col items-center justify-center gap-2">
              <p className="text-muted-foreground text-sm">
                No articles found.
              </p>
            </div>
          )}
        </div>
      ) : (
        <ArticleTable articles={articles} />
      )}
    </>
  );
}
