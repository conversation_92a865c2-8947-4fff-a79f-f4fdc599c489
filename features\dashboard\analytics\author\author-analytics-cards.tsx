'use client';

import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import AnalyticsCards from '@/features/dashboard/analytics/analytic-cards';
import type { TPageCard } from '@/features/dashboard/analytics/types';
import { getIconByStatus } from '@/features/dashboard/shared/icons';

export default function AuthorAnalyticsCards() {
  const totalDraftArticles = useQuery(api.author.getTotalArticlesByStatus, {
    status: 'draft',
  });
  const totalStagedArticles = useQuery(api.author.getTotalArticlesByStatus, {
    status: 'staged',
  });
  const totalApprovedArticles = useQuery(api.author.getTotalArticlesByStatus, {
    status: 'approved',
  });

  const totalDeletedArticles = useQuery(api.author.getTotalArticlesByStatus, {
    status: 'deleted',
  });

  const isLoading =
    totalDraftArticles === undefined ||
    totalStagedArticles === undefined ||
    totalApprovedArticles === undefined ||
    totalDeletedArticles === undefined;

  if (isLoading) {
    return <div>Loading analytics...</div>;
  }
  const cards: TPageCard[] = [
    {
      title: 'Draft',
      value: totalDraftArticles,
      badgeText: 'All',
      icon: getIconByStatus('draft'),
      description: 'Only visible to the author',
      footer: 'Author drafts',
    },
    {
      title: 'Staged',
      value: totalStagedArticles,
      badgeText: 'All',
      icon: getIconByStatus('staged'),
      description: 'Visible to administrators',
      footer: 'Awaiting admin review',
    },
    {
      title: 'Approved',
      value: totalApprovedArticles,
      badgeText: 'All',
      icon: getIconByStatus('approved'),
      description: 'Approved articles by admin',
      footer: 'Visible to the staff',
    },
    {
      title: 'Deleted',
      value: totalDeletedArticles,
      badgeText: 'All',
      icon: getIconByStatus('deleted'),
      description: 'Marked for removal by author',
      footer: 'Deleted articles',
    },
  ];
  return <AnalyticsCards cards={cards} />;
}
