'use client';
import { useQuery } from 'convex/react';
import { formatDistanceToNow } from 'date-fns';
import { useSearchParams } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { api } from '@/convex/_generated/api';
import { getShortTimeAgo } from '@/lib/utils';
import profile from '@/public/profile.svg';

export default function ActivityList() {
  const activities = useQuery(api.activity.getActivities);
  const user = useQuery(api.users.getUser);
  const searchParams = useSearchParams();
  if (!(activities && Array.isArray(activities))) {
    return null;
  }
  const types = searchParams.getAll('type');
  const selectedTypes = types.length ? types : ['all'];

  const filteredActivities = selectedTypes.includes('all')
    ? activities
    : activities.filter((activity) => selectedTypes.includes(activity.action));

  return (
    <div className="flex w-full flex-col gap-6">
      <h2 className="font-bold text-lg text-muted-foreground tracking-tight">
        <span className="text-primary capitalize">
          {selectedTypes.join(' , ')}{' '}
        </span>{' '}
        activities
      </h2>

      <div className="flex flex-col gap-6">
        {filteredActivities.map((activity) => (
          <div
            className="flex w-full items-center justify-between"
            key={activity._id}
          >
            <div className="flex items-center gap-4">
              <div>
                <Avatar className="size-8 cursor-pointer rounded-full">
                  <AvatarImage
                    alt={'user'}
                    src={user?.avatarUrl || profile.src}
                  />
                  <AvatarFallback className="rounded-full">
                    {user?.name?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </div>
              <div>
                <p className="max-w-64 text-primary/80 text-sm md:max-w-96 lg:max-w-xl">
                  You{' '}
                  <span className="font-medium text-primary">
                    {activity.action}
                  </span>{' '}
                  "{activity.docTitle}" {activity.target.docType} with status of{' '}
                  {activity.docStatus}
                </p>
              </div>
            </div>
            <p>
              <span className="hidden text-muted-foreground md:block">
                {formatDistanceToNow(new Date(activity._creationTime), {
                  addSuffix: true,
                })}
              </span>
              <span className="text-muted-foreground md:hidden">
                {getShortTimeAgo(new Date(activity._creationTime))}
              </span>
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
