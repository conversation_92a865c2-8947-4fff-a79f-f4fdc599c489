import {
  FileCheck2Icon,
  FileClockIcon,
  FileIcon,
  FileOutputIcon,
  FilePenIcon,
  FileX2Icon,
} from 'lucide-react';

type TStatus = 'draft' | 'staged' | 'approved' | 'published' | 'deleted';

export const getIconByStatus = (status: TStatus | string) => {
  switch (status) {
    case 'draft':
      return FilePenIcon;
    case 'staged':
      return FileClockIcon;
    case 'approved':
      return FileCheck2Icon;
    case 'published':
      return FileOutputIcon;
    case 'deleted':
      return FileX2Icon;
    default:
      return FileIcon;
  }
};
