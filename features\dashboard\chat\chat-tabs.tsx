'use client';
import { useQuery } from 'convex/react';
import { format } from 'date-fns';
import { MenuIcon } from 'lucide-react';
import { useState } from 'react';
import {
  Ta<PERSON>,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/custom/chat-tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { cn } from '@/lib/utils';
import profileImg from '@/public/profile.svg';
import SearchTop from '../shared/search/search-top';
import ChatTabContent from './chat-tab-content';
import StartConversation from './start-conversation';
export default function ChatTabs() {
  const allConversations = useQuery(api.chat.getConversations);
  const [searchText, setSearchText] = useState('');
  const [selectedTab, setSelectedTab] = useState<Id<'users'> | null>(null);
  const searchResults = useQuery(api.chat.getConversationsSearch, {
    query: searchText,
  });

  if (!allConversations) {
    return null;
  }
  // sort converstion for those with new messages to come at top
  allConversations.sort((a, b) => b.unreadCount - a.unreadCount);
  // const conversations = searchText ? searchResults : allConversations;
  const conversations = searchText
    ? [
        ...(searchResults || []),
        ...allConversations.filter(
          (conv) =>
            !(searchResults || []).some(
              (res) => res.otherUser._id === conv.otherUser._id
            )
        ),
      ]
    : allConversations;

  if (!conversations) {
    return null;
  }

  return (
    <div>
      <Tabs
        className="relative flex flex-col gap-3 lg:flex-row"
        onValueChange={(val) => setSelectedTab(val as Id<'users'>)}
        value={selectedTab || conversations[0]?.otherUser._id || ''}
      >
        <div className="sticky top-16 hidden lg:block">
          <div className="flex items-center justify-between gap-2">
            <SearchTop searchText={searchText} setSearchText={setSearchText} />
            <StartConversation iconOnly />
          </div>
          <TabsList className="flex w-full min-w-2xs flex-col gap-4 py-4">
            {conversations.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-4 text-center text-muted-foreground text-sm">
                <p>No conversations yet</p>
              </div>
            ) : (
              conversations.map((conversation) => {
                return (
                  <TabsTrigger
                    asChild
                    key={conversation.otherUser._id}
                    value={conversation.otherUser._id}
                  >
                    <Button
                      className="flex h-fit w-full items-center justify-between gap-3 px-3 text-left"
                      variant={'ghost'}
                    >
                      <Avatar className="size-10 ">
                        <AvatarImage
                          src={
                            conversation.otherUser.avatarUrl || profileImg.src
                          }
                        />
                        <AvatarFallback>
                          {conversation.otherUser.name?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-1 flex-col gap-1">
                        <div className="flex items-center justify-between gap-1">
                          <p className="font-medium text-sm capitalize">
                            {conversation.otherUser.username}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            {format(
                              new Date(
                                conversation?.lastMessage?._creationTime || 0
                              ),
                              'hh:mm a'
                            )}
                          </p>
                        </div>
                        <div className="flex items-center justify-between gap-1">
                          <p className="max-w-3xs truncate text-muted-foreground text-sm">
                            {conversation?.lastMessage?.isDeleted ? (
                              <span className="italic">
                                This message was deleted.
                              </span>
                            ) : (
                              conversation?.lastMessage?.content || 'Say hi!'
                            )}
                          </p>
                          {conversation?.unreadCount > 0 && (
                            <Badge className="size-4 min-w-4 rounded-full px-1 font-mono tabular-nums">
                              {conversation.unreadCount}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </Button>
                  </TabsTrigger>
                );
              })
            )}
          </TabsList>
        </div>
        <div className="flex items-center justify-between gap-2 lg:hidden">
          <Drawer>
            <DrawerTrigger asChild>
              <Button
                className="bg-background lg:hidden"
                size={'icon'}
                variant={'outline'}
              >
                <MenuIcon className="size-5" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DrawerTrigger>
            <DrawerContent className="px-3 py-2">
              <DrawerHeader className="sr-only">
                <DrawerTitle>Settings</DrawerTitle>
                <DrawerDescription>
                  Settings navigation links.
                </DrawerDescription>
              </DrawerHeader>
              <SearchTop
                searchText={searchText}
                setSearchText={setSearchText}
              />
              <TabsList className="flex flex-col gap-5 py-4">
                {conversations.map((conversation) => {
                  return (
                    <TabsTrigger
                      asChild
                      key={conversation.otherUser._id}
                      value={conversation.otherUser._id}
                    >
                      <Button
                        className="flex h-fit w-full items-center justify-between gap-3 px-3 text-left"
                        variant={'ghost'}
                      >
                        <Avatar className="size-10">
                          <AvatarImage
                            src={
                              conversation.otherUser.avatarUrl || profileImg.src
                            }
                          />
                          <AvatarFallback>
                            {conversation.otherUser.name?.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex flex-1 flex-col gap-1">
                          <div className="flex items-center justify-between gap-1">
                            <p className="font-medium text-sm capitalize">
                              {conversation.otherUser.username}
                            </p>
                            <p className="text-muted-foreground text-xs">
                              {format(
                                new Date(
                                  conversation?.lastMessage?._creationTime || 0
                                ),
                                'hh:mm a'
                              )}
                            </p>
                          </div>
                          <div className="flex items-center justify-between gap-1">
                            <p className="max-w-3xs truncate text-muted-foreground text-sm">
                              {conversation?.lastMessage?.isDeleted ? (
                                <span className="italic">
                                  This message was deleted.
                                </span>
                              ) : (
                                conversation?.lastMessage?.content || 'Say hi!'
                              )}
                            </p>
                            {conversation?.unreadCount > 0 && (
                              <Badge className="size-4 min-w-4 rounded-full px-1 font-mono tabular-nums">
                                {conversation.unreadCount}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </Button>
                    </TabsTrigger>
                  );
                })}
              </TabsList>
            </DrawerContent>
          </Drawer>
          <StartConversation className="size-9" iconOnly />
        </div>
        <div
          className={cn(
            'flex min-h-[calc(100vh-10.1rem)] w-full flex-1 rounded-lg bg-background lg:min-h-screen',
            allConversations.length === 0 && 'flex items-center justify-center '
          )}
        >
          {allConversations.length > 0 ? (
            allConversations.map((conversation) => (
              <TabsContent
                key={conversation.otherUser._id}
                value={conversation.otherUser._id}
              >
                <ChatTabContent otherUser={conversation.otherUser} />
              </TabsContent>
            ))
          ) : (
            <div className="flex h-full w-full flex-col items-center justify-center gap-4">
              <p className="text-muted-foreground text-sm">
                No conversations yet
              </p>

              <StartConversation />
            </div>
          )}
        </div>
      </Tabs>
    </div>
  );
}
