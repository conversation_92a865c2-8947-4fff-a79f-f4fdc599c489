import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { requireUser } from "./users";

export const sendMessage = mutation({
  args: {
    receiverId: v.id("users"),
    receiverName: v.string(),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await requireUser(ctx);
    const sender = await ctx.db.get(userId);
    if (!sender || !sender.username) {
      throw new Error("Not authenticated");
    }

    if (userId === args.receiverId) {
      throw new Error("Not authenticated");
    }
    if (args.content.trim() === "") {
      throw new Error("Message cannot be empty");
    }
    return await ctx.db.insert("chatMessages", {
      ...args,
      senderName: sender.username,
      senderId: userId,
      isRead: false,
      isDeleted: false,
    });
  },
});
export const startConversation = mutation({
  args: {
    otherUserId: v.id("users"),
    receiverName: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await requireUser(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }
    const sender = await ctx.db.get(userId);
    if (!sender || !sender.username) {
      throw new Error("Not authenticated");
    }
    return await ctx.db.insert("chatMessages", {
      senderId: userId,
      senderName: sender.username,
      receiverId: args.otherUserId,
      receiverName: args.receiverName,
      content: `Hello ${args.receiverName}!`,
      isRead: false,
      isDeleted: false,
    });
  },
});
export const getConversation = query({
  args: {
    otherUserId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const userId = await requireUser(ctx);
    if (!userId) {
      return [];
    }

    // Get all messages between the two users
    const messages = await ctx.db
      .query("chatMessages")
      .filter((q) =>
        q.or(
          q.and(
            q.eq(q.field("senderId"), userId),
            q.eq(q.field("receiverId"), args.otherUserId)
          ),
          q.and(
            q.eq(q.field("senderId"), args.otherUserId),
            q.eq(q.field("receiverId"), userId)
          )
        )
      )
      .order("asc")
      .collect();

    // Get user details for each message
    const messagesWithUsers = await Promise.all(
      messages.map(async (message) => {
        const sender = await ctx.db.get(message.senderId);
        const receiver = await ctx.db.get(message.receiverId);
        return {
          ...message,
          sender,
          receiver,
        };
      })
    );

    return messagesWithUsers;
  },
});

export const getConversations = query({
  args: {},
  handler: async (ctx) => {
    const userId = await requireUser(ctx);
    if (!userId) {
      return [];
    }

    // Get all messages where user is sender or receiver
    const messages = await ctx.db
      .query("chatMessages")
      .filter((q) =>
        q.or(
          q.eq(q.field("senderId"), userId),
          q.eq(q.field("receiverId"), userId)
        )
      )
      .order("desc")
      .collect();

    // Group by conversation partner and get the latest message
    const conversationMap = new Map();

    for (const message of messages) {
      const otherUserId =
        message.senderId === userId ? message.receiverId : message.senderId;

      if (!conversationMap.has(otherUserId)) {
        const otherUser = await ctx.db.get(otherUserId);
        conversationMap.set(otherUserId, {
          // otherUser, and their avatarUrl
          otherUser: {
            ...otherUser,
            avatarUrl: otherUser!.imageId
              ? await ctx.storage.getUrl(otherUser!.imageId)
              : otherUser!.image,
          },
          lastMessage: message,
          unreadCount: 0,
        });
      }

      // Count unread messages from the other user
      if (message.receiverId === userId && !message.isRead) {
        const conversation = conversationMap.get(otherUserId);
        conversation.unreadCount++;
      }
    }

    return Array.from(conversationMap.values());
  },
});

export const markAsRead = mutation({
  args: {
    otherUserId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const userId = await requireUser(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Mark all messages from the other user as read
    const unreadMessages = await ctx.db
      .query("chatMessages")
      .filter((q) =>
        q.and(
          q.eq(q.field("senderId"), args.otherUserId),
          q.eq(q.field("receiverId"), userId),
          q.eq(q.field("isRead"), false)
        )
      )
      .collect();

    for (const message of unreadMessages) {
      await ctx.db.patch(message._id, { isRead: true });
    }
  },
});

// delete my message
export const deleteMessage = mutation({
  args: {
    messageId: v.id("chatMessages"),
  },
  handler: async (ctx, args) => {
    const userId = await requireUser(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }
    if (message.senderId !== userId) {
      throw new Error("Unauthorized");
    }
    await ctx.db.patch(args.messageId, { isDeleted: true });
  },
});
export const undoDeleteMessage = mutation({
  args: {
    messageId: v.id("chatMessages"),
  },
  handler: async (ctx, args) => {
    const userId = await requireUser(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }
    if (message.senderId !== userId) {
      throw new Error("Unauthorized");
    }
    await ctx.db.patch(args.messageId, { isDeleted: false });
  },
});

export const searchSenderName = query({
  args: {
    query: v.string(),
  },
  handler: async (ctx, { query }) => {
    try {
      const users = await ctx.db
        .query("chatMessages")
        .withSearchIndex("search_senderName", (q) =>
          q.search("senderName", query)
        )
        .collect();
      return users;
    } catch {
      return { success: false, error: "Failed to search sender name." };
    }
  },
});

export const getConversationsSearch = query({
  args: {
    query: v.string(),
  },
  handler: async (ctx, { query }) => {
    const userId = await requireUser(ctx);
    if (!userId) {
      return [];
    }

    // Get all messages where user is sender or receiver
    const messages = await ctx.db
      .query("chatMessages")
      .withSearchIndex("search_senderName", (q) =>
        q.search("senderName", query)
      )
      .filter((q) =>
        q.or(
          q.eq(q.field("senderId"), userId),
          q.eq(q.field("receiverId"), userId)
        )
      )
      .collect();

    // Group by conversation partner and get the latest message
    const conversationMap = new Map();

    for (const message of messages) {
      const otherUserId =
        message.senderId === userId ? message.receiverId : message.senderId;

      if (!conversationMap.has(otherUserId)) {
        const otherUser = await ctx.db.get(otherUserId);
        conversationMap.set(otherUserId, {
          // otherUser, and their avatarUrl
          otherUser: {
            ...otherUser,
            avatarUrl: otherUser!.imageId
              ? await ctx.storage.getUrl(otherUser!.imageId)
              : otherUser!.image,
          },
          lastMessage: message,
          unreadCount: 0,
        });
      }

      // Count unread messages from the other user
      if (message.receiverId === userId && !message.isRead) {
        const conversation = conversationMap.get(otherUserId);
        conversation.unreadCount++;
      }
    }

    return Array.from(conversationMap.values());
  },
});
