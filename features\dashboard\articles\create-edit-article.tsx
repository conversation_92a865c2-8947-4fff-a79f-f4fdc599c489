'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { cn } from '@/lib/utils';
import { articleSchema, type TArticleSchema } from './schema';

interface CreateOrUpdateArticleProps {
  openDialog?: boolean;
  setOpenDialog?: (open: boolean) => void;
  title?: string;
  description?: string;
  id?: Id<'articles'>;
}

export default function CreateOrUpdateArticle({
  openDialog,
  setOpenDialog,
  title,
  description,
  id,
}: CreateOrUpdateArticleProps) {
  const isEditMode = Boolean(id);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [internalOpen, setInternalOpen] = useState(false);
  const createArticleMutation = useMutation(api.articles.createArticle);
  const updateArticleMutation = useMutation(api.articles.updateArticle);

  const open = openDialog ?? internalOpen;
  const setOpen = setOpenDialog ?? setInternalOpen;

  const form = useForm<TArticleSchema>({
    resolver: zodResolver(articleSchema),
    defaultValues: {
      title: title || '',
      description: description || '',
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        title: title || '',
        description: description || '',
      });
    }
  }, [open, title, description, form]);

  const handleSubmit = async (data: TArticleSchema) => {
    setIsSubmitting(true);
    setOpen(false);

    try {
      if (isEditMode) {
        if (!id) {
          toast.error('Failed to get article id.');
          return;
        }
        const result = await updateArticleMutation({
          id,
          ...data,
        });
        if (result?.success) {
          form.reset();
          toast.success('Article updated successfully!');
        } else {
          toast.error(result.error);
        }
      } else {
        const result = await createArticleMutation(data);
        if (result?.success) {
          form.reset();
          toast.success('Article created successfully!');
        } else {
          toast.error(result.error);
        }
      }
    } catch {
      toast.error('Something went wrong.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog onOpenChange={setOpen} open={open}>
      {!isEditMode && (
        <DialogTrigger
          className={cn(
            buttonVariants({ variant: 'default', size: 'lg' }),
            'cursor-pointer rounded-sm px-3'
          )}
        >
          Add New...
        </DialogTrigger>
      )}

      <DialogContent className="flex w-full max-w-md flex-col">
        <DialogHeader className="sr-only">
          <DialogTitle>
            {isEditMode ? 'Update' : 'Create New'} Article
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            className="mt-6 space-y-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Title" {...field} />
                  </FormControl>
                  <FormDescription>
                    The title that identifies your draft.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Input placeholder="Description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button className="w-full" disabled={isSubmitting} type="submit">
              {isSubmitting ? (
                <Spinner text="Submitting..." />
              ) : isEditMode ? (
                'Update Article'
              ) : (
                'Create Article'
              )}
            </Button>
          </form>
        </Form>
        <DialogClose />
      </DialogContent>
    </Dialog>
  );
}
