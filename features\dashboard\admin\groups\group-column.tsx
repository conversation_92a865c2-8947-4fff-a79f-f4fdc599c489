import type { ColumnDef } from '@tanstack/react-table';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import { DataTableColumnHeader } from '@/components/custom/data-table-column-header';
import { DragHandle } from '@/components/custom/drag-handle';
import { Badge } from '@/components/ui/badge';
import { capitalize, truncate } from '@/lib/utils';
import GroupTableColumnActions from './column-actions';
import type { TGroup } from './group-card';
export const groupColumns: ColumnDef<TGroup>[] = [
  {
    id: 'dragHandle',
    header: '',
    cell: () => <DragHandle />,
    meta: {
      label: 'Drag Handle',
    },
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      const groupName = (row.getValue('name') as string) ?? '';
      return (
        <Link
          className="flex items-center gap-4 font-medium decoration-dashed underline-offset-4 hover:underline"
          href={'/admin/groups'}
        >
          {truncate(capitalize(groupName), 40)}
        </Link>
      );
    },
    meta: {
      label: 'Name',
    },
  },
  {
    accessorKey: 'description',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Description" />
    ),
    cell: ({ row }) => {
      const groupDescription = (row.getValue('description') as string) ?? '';
      return (
        <span className="text-muted-foreground">
          {groupDescription.length > 40 ? (
            <span>{groupDescription.slice(0, 40)}...</span>
          ) : (
            <span>{groupDescription}</span>
          )}
          {groupDescription.length === 0 && (
            <span className="text-muted-foreground">No description</span>
          )}
        </span>
      );
    },
    meta: {
      label: 'Description',
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return (
        <Badge variant={status === 'approved' ? 'default' : 'secondary'}>
          {status}
        </Badge>
      );
    },
    meta: {
      label: 'Status',
    },
  },
  {
    accessorKey: '_creationTime',
    header: 'Created At',
    cell: ({ row }) => {
      return (
        <span className="text-muted-foreground">
          {formatDistanceToNow(new Date(row.getValue('_creationTime')), {
            addSuffix: true,
          })}
        </span>
      );
    },
    meta: {
      label: 'Created At',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => <GroupTableColumnActions row={row} />,
    meta: {
      label: 'Actions',
    },
  },
];
