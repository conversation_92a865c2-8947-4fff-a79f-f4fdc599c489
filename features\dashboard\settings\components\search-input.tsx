'use client';

import { LoaderCircleIcon, SearchIcon } from 'lucide-react';
import { useEffect, useId, useState } from 'react';

import { Input } from '@/components/ui/input';

export default function SearchInput() {
  const id = useId();
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (inputValue) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
    setIsLoading(false);
  }, [inputValue]);

  return (
    <div className="mb-10">
      <div className="relative min-h-10 bg-background">
        <Input
          className="peer min-h-10 ps-9"
          id={id}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Search..."
          type="search"
          value={inputValue}
        />
        <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
          {isLoading ? (
            <LoaderCircleIcon
              aria-label="Loading..."
              className="animate-spin"
              role="status"
              size={16}
            />
          ) : (
            <SearchIcon aria-hidden="true" size={16} />
          )}
        </div>
      </div>
    </div>
  );
}
