'use client';
import { type Preloaded, usePreloadedQuery } from 'convex/react';
import { ArmchairIcon } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { PageContainer } from '@/components/custom/page-container';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import type { api } from '@/convex/_generated/api';
import profile from '@/public/profile.svg';
import { ChatNotifications } from '../notifications/components/chat-notifications';
import { CommandMenuTop } from './command-menu';
import { PopoverDocsTop } from './docs-top';
import FeedbackForm from './feedback-form';
import HeaderNavs, { type UserRole } from './header-navs';
import { MobileNav } from './mobile-nav';
import { UserDropdown } from './user-dropdown';

export default function SiteHeader({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);
  const pathname = usePathname();
  const basePath = `/${pathname.split('/')[1]}`;
  if (!user) {
    return null;
  }

  return (
    <header className="-top-[4.25rem] sticky z-20 ">
      <PageContainer className="bg-background px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Link className="hidden sm:flex" href="/">
              <ArmchairIcon className="size-6" />
            </Link>
            <div className="hidden sm:flex">
              <svg
                className="size-4 fill-current text-muted-foreground opacity-25"
                height="16"
                strokeLinejoin="round"
                viewBox="0 0 16 16"
                width="16"
              >
                <title>Slash</title>
                <path
                  clipRule="evenodd"
                  d="M4.01526 15.3939L4.3107 14.7046L10.3107 0.704556L10.6061 0.0151978L11.9849 0.606077L11.6894 1.29544L5.68942 15.2954L5.39398 15.9848L4.01526 15.3939Z"
                  fillRule="evenodd"
                />
              </svg>
            </div>
            <div className="flex items-center gap-2">
              <Avatar className="size-5">
                <AvatarImage src={user.avatarUrl || profile.src} />
                <AvatarFallback>{user.username?.charAt(0)}</AvatarFallback>
              </Avatar>
              <p className="font-medium text-sm tracking-tight">
                {user.username}&apos;s dashboard
              </p>
              <Badge className="hidden capitalize sm:flex" variant="secondary">
                {user.role}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-2 ">
            {/* <TopFindPopover /> */}
            <CommandMenuTop
              basePath={basePath}
              role={user.role as UserRole}
              username={user.username || ''}
            />
            <FeedbackForm />
            <ChatNotifications />
            <PopoverDocsTop />
            <UserDropdown preloadedUser={preloadedUser} />
            <MobileNav basePath={basePath} />
          </div>
        </div>
      </PageContainer>
      <HeaderNavs role={user.role as UserRole} />
    </header>
  );
}
