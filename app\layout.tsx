import type { Metada<PERSON> } from 'next';
import '@/styles/globals.css';

import { ConvexAuthNextjsServerProvider } from '@convex-dev/auth/nextjs/server';
import { GeistMono } from 'geist/font/mono';
import { Geist<PERSON>ans } from 'geist/font/sans';
import { ConvexClientProvider } from '@/components/convex-client-provider';
import { ThemeProvider } from '@/components/mode/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { siteConfig } from '@/config/web';

export const metadata: Metadata = {
  title: {
    absolute: 'Better Flow',
    template: `%s - ${siteConfig.name}`,
  },
  description: siteConfig.description,
  openGraph: {
    title: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    siteName: siteConfig.name,
    images: [siteConfig.ogImage],
    locale: 'en-US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    site: siteConfig.url,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ConvexAuthNextjsServerProvider>
      <html
        className={`${GeistSans.variable} ${GeistMono.variable}`}
        lang="en"
        suppressHydrationWarning
      >
        <body>
          <ConvexClientProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              disableTransitionOnChange
              enableSystem
            >
              {children}
              <Toaster />
            </ThemeProvider>
          </ConvexClientProvider>
        </body>
      </html>
    </ConvexAuthNextjsServerProvider>
  );
}
