import {
  ArmchairIcon,
  CirclePlusIcon,
  CircleUserIcon,
  LayoutPanelTopIcon,
  LogOutIcon,
} from "lucide-react";

export const userDropdownGroup1 = [
  {
    label: "Dashboard",
    href: "/",
    icon: LayoutPanelTopIcon,
  },
  {
    label: "Account Settings",
    href: "/settings",
    icon: CircleUserIcon,
  },
  {
    label: "Create Team",
    href: "/team",
    icon: CirclePlusIcon,
  },
];

export const userDropdownGroup2 = [
  {
    label: "Command Menu",
    href: "",
    custom: "command", // indicates special rendering (Ctrl + K)
  },
  {
    label: "Theme",
    href: "",
    custom: "theme", // indicates theme switcher UI
  },
];

export const userDropdownGroup3 = [
  {
    label: "Home Page",
    href: "/",
    icon: ArmchairIcon,
  },
  {
    label: "Logout",
    href: "/sign-in",
    icon: LogOutIcon,
  },
];

export const authorNavItems = [
  {
    label: "Overview",
    link: "/author",
  },
  {
    label: "Editor",
    link: "/author/editor",
  },
  {
    label: "Media",
    link: "/author/media",
  },
  {
    label: "Activity",
    link: "/author/activity",
  },
  {
    label: "Chat",
    link: "/author/chat",
  },
  {
    label: "Analytics",
    link: "/author/analytics",
  },
  {
    label: "Support",
    link: "/author/support",
  },
  {
    label: "Settings",
    link: "/author/settings",
  },
];
export const adminNavItems = [
  {
    label: "Overview",
    link: "/admin",
  },
  {
    label: "Staff",
    link: "/admin/staff",
  },
  {
    label: "Articles",
    link: "/admin/articles",
  },
  {
    label: "Media",
    link: "/admin/media",
  },
  {
    label: "Ads",
    link: "/admin/ads",
  },
  {
    label: "Groups",
    link: "/admin/groups",
  },
  {
    label: "Activity",
    link: "/admin/activity",
  },
  {
    label: "Chat",
    link: "/admin/chat",
  },
  {
    label: "Analytics",
    link: "/admin/analytics",
  },
  {
    label: "Storage",
    link: "/admin/storage",
  },
  {
    label: "Support",
    link: "/admin/support",
  },
  {
    label: "Settings",
    link: "/admin/settings",
  },
];

export const mediaNavItems = [
  {
    label: "Overview",
    link: "/media-manager",
  },
  {
    label: "Upload",
    link: "/media-manager/upload",
  },
  {
    label: "Media",
    link: "/media-manager/media",
  },
  {
    label: "Activity",
    link: "/media-manager/activity",
  },
  {
    label: "Chat",
    link: "/media-manager/chat",
  },
  {
    label: "Analytics",
    link: "/media-manager/analytics",
  },
  {
    label: "Support",
    link: "/media-manager/support",
  },
  {
    label: "Settings",
    link: "/media-manager/settings",
  },
];

export const settingLinks = [
  {
    label: "General",
    href: "/settings",
  },
  {
    label: "Authentications",
    href: "/settings/auth",
  },
  {
    label: "Billing",
    href: "/settings/billing",
  },
];

export const footerLinks = [
  {
    label: "Home",
    href: "/",
  },
  {
    label: "Docs",
    href: "/docs",
  },
  {
    label: "Guides",
    href: "/guide",
  },
  {
    label: "Help",
    href: "/help",
  },
  {
    label: "Contact",
    href: "/contact",
  },
  {
    label: "Legal",
    href: "/legal",
  },
];
