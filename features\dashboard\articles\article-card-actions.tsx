import { type ReactMutation, useMutation, useQuery } from 'convex/react';
import type { FunctionReference, FunctionReturnType } from 'convex/server';
import {
  CircleCheckBigIcon,
  EllipsisIcon,
  PenToolIcon,
  StarIcon,
  TrashIcon,
  UndoIcon,
} from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import AddGroupToArticle from './add-group';
import CreateOrUpdateArticle from './create-edit-article';

export type ArticleType = FunctionReturnType<typeof api.articles.getArticle>;

interface ArticleCardActionsProps {
  article: ArticleType;
}

export function ArticleCardActions({ article }: ArticleCardActionsProps) {
  const user = useQuery(api.users.getUser);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  // Mutations
  const deleteArticle = useMutation(api.articles.deleteArticle);
  const restoreArticle = useMutation(api.articles.restoreArticle);
  const toggleFavoriteArticle = useMutation(api.articles.toggleFavoriteArticle);
  const stageArticle = useMutation(api.articles.stageArticle);
  const unStageArticle = useMutation(api.articles.unStageArticle);
  const approveArticle = useMutation(api.admin.approveArticle);
  const rejectArticle = useMutation(api.admin.rejectArticle);
  const publishArticle = useMutation(api.admin.publishArticle);
  const unapproveArticle = useMutation(api.admin.unapproveArticle);

  // Helper to handle async actions with toast feedback
  const handleAction = useCallback(
    async (
      mutation: ReactMutation<
        FunctionReference<
          'mutation',
          'public',
          {
            id: Id<'articles'>;
          },
          | {
              success: boolean;
              error: string;
            }
          | {
              success: boolean;
              error?: undefined;
            },
          string | undefined
        >
      >,
      params: { id: Id<'articles'> },
      successMsg: string,
      errorMsg: string
    ) => {
      try {
        await mutation(params);
        toast.success(successMsg);
      } catch {
        toast.error(errorMsg);
      }
    },
    []
  );
  // Guard clauses
  if (!article || 'success' in article) {
    return null;
  }
  if (!user || 'success' in user) {
    return null;
  }
  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button size="icon" variant="ghost">
            <EllipsisIcon />
          </Button>
        </PopoverTrigger>

        <PopoverContent className="-right-11 absolute w-56 px-2 py-4">
          <div className="flex flex-col">
            {/* Favorite toggle */}
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() =>
                handleAction(
                  toggleFavoriteArticle,
                  { id: article._id },
                  article.isFavorite
                    ? 'Article removed from favorite!'
                    : 'Article added to favorite!',
                  'Failed to toggle favorite article.'
                )
              }
              variant="ghost"
            >
              <span>
                {article.isFavorite
                  ? 'Remove from favorite'
                  : 'Add to favorite'}
              </span>
              <StarIcon
                className={`size-4 ${
                  article.isFavorite
                    ? 'fill-primary text-muted-foreground'
                    : 'text-muted-foreground'
                }`}
              />
            </Button>

            {/* Edit */}
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() => setOpenEditDialog(true)}
              variant="ghost"
            >
              <span>Edit</span>
              <PenToolIcon className="size-4 text-muted-foreground" />
            </Button>

            {/* Delete / Restore */}
            {article.status === 'deleted' ? (
              <Button
                className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                onClick={() =>
                  handleAction(
                    restoreArticle,
                    { id: article._id },
                    'Article restored successfully!',
                    'Failed to restore article.'
                  )
                }
                variant="ghost"
              >
                <span>Restore</span>
                <UndoIcon className="size-4 text-muted-foreground" />
              </Button>
            ) : (
              <Button
                className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                onClick={() =>
                  handleAction(
                    deleteArticle,
                    { id: article._id },
                    'Article deleted successfully!',
                    'Failed to delete article.'
                  )
                }
                variant="ghost"
              >
                <span>Delete</span>
                <TrashIcon className="size-4 text-muted-foreground" />
              </Button>
            )}

            {/* Author actions */}
            {user.role === 'author' && (
              <div>
                {article.status === 'staged' ? (
                  <Button
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        unStageArticle,
                        { id: article._id },
                        'Article unstaged successfully!',
                        'Failed to unstage article.'
                      )
                    }
                    variant="ghost"
                  >
                    <span>Unstage</span>
                    <UndoIcon className="size-4 text-muted-foreground" />
                  </Button>
                ) : (
                  <Button
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        stageArticle,
                        { id: article._id },
                        'Article staged successfully!',
                        'Failed to stage article.'
                      )
                    }
                    variant="ghost"
                  >
                    <span>Stage</span>
                    <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                  </Button>
                )}
              </div>
            )}

            {/* Admin actions */}
            {user.role === 'admin' && (
              <>
                {article.status === 'staged' && (
                  <>
                    <Button
                      className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                      onClick={() =>
                        handleAction(
                          rejectArticle,
                          { id: article._id },
                          'Article rejected successfully!',
                          'Failed to reject article.'
                        )
                      }
                      variant="ghost"
                    >
                      <span>Reject</span>
                      <TrashIcon className="size-4 text-muted-foreground" />
                    </Button>
                    <Button
                      className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                      onClick={() =>
                        handleAction(
                          approveArticle,
                          { id: article._id },
                          'Article approved successfully!',
                          'Failed to approve article.'
                        )
                      }
                      variant="ghost"
                    >
                      <span>Approve</span>
                      <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                    </Button>
                  </>
                )}

                {article.status === 'approved' && (
                  <>
                    <Button
                      className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                      onClick={() =>
                        handleAction(
                          unapproveArticle,
                          { id: article._id },
                          'Article unapproved successfully!',
                          'Failed to unapprove article.'
                        )
                      }
                      variant="ghost"
                    >
                      <span>Unapprove</span>
                      <UndoIcon className="size-4 text-muted-foreground" />
                    </Button>
                    <Button
                      className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                      onClick={() =>
                        handleAction(
                          publishArticle,
                          { id: article._id },
                          'Article published successfully!',
                          'Failed to publish article.'
                        )
                      }
                      variant="ghost"
                    >
                      <span>Publish</span>
                      <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                    </Button>
                  </>
                )}

                {article.status === 'published' && (
                  <Button
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        unapproveArticle,
                        { id: article._id },
                        'Article unpublished successfully!',
                        'Failed to unpublish article.'
                      )
                    }
                    variant="ghost"
                  >
                    <span>Unpublish</span>
                    <UndoIcon className="size-4 text-muted-foreground" />
                  </Button>
                )}

                <AddGroupToArticle
                  articleId={article._id}
                  prevGroupId={article.groupId as Id<'groups'>}
                />
              </>
            )}
          </div>
        </PopoverContent>
      </Popover>

      <CreateOrUpdateArticle
        description={article.description}
        id={article._id}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
        title={article.title}
      />
    </>
  );
}
