import { type ReactMutation, useMutation } from 'convex/react';
import type { FunctionReference } from 'convex/server';
import { EllipsisIcon, PenToolIcon, TrashIcon, UndoIcon } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import CreateOrUpdateGroup from './create-update-group';
import type { TGroup } from './group-card';

export default function GroupCardActions({ group }: { group: TGroup }) {
  const deleteGroup = useMutation(api.groups.deleteGroup);
  const restoreGroup = useMutation(api.groups.restoreGroup);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleAction = useCallback(
    async (
      mutation: ReactMutation<
        FunctionReference<
          'mutation',
          'public',
          {
            groupId: Id<'groups'>;
          },
          | {
              success: boolean;
              error: string;
            }
          | {
              success: boolean;
              error?: undefined;
            },
          string | undefined
        >
      >,
      params: { groupId: Id<'groups'> },
      successMsg: string,
      errorMsg: string
    ) => {
      try {
        await mutation(params);
        toast.success(successMsg);
      } catch {
        toast.error(errorMsg);
      }
    },
    []
  );
  if (!group || 'success' in group) {
    return null;
  }
  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button size="icon" variant="ghost">
            <EllipsisIcon />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="-right-11 absolute w-56 px-2 py-4">
          <div className="flex flex-col">
            <Button
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() => setOpenEditDialog(true)}
              variant="ghost"
            >
              <span>Edit</span>
              <PenToolIcon className="size-4 text-muted-foreground" />
            </Button>
            {group.status === 'approved' ? (
              <Button
                className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                onClick={() =>
                  handleAction(
                    deleteGroup,
                    { groupId: group._id },
                    'Group deleted successfully!',
                    'Failed to delete group.'
                  )
                }
                variant="ghost"
              >
                <span>Delete</span>
                <TrashIcon className="size-4 text-muted-foreground" />
              </Button>
            ) : (
              <Button
                className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                onClick={() =>
                  handleAction(
                    restoreGroup,
                    { groupId: group._id },
                    'Group restored successfully!',
                    'Failed to restore group.'
                  )
                }
                variant="ghost"
              >
                <span>Restore</span>
                <UndoIcon className="size-4 text-muted-foreground" />
              </Button>
            )}
          </div>
        </PopoverContent>
      </Popover>
      <CreateOrUpdateGroup
        description={group.description}
        id={group._id}
        name={group.name}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
      />
    </>
  );
}
