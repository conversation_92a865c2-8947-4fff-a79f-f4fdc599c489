// passwordReset.ts
import Resend from "@auth/core/providers/resend";
import { RandomReader, generateRandomString } from "@oslojs/crypto/random";
import { Resend as ResendSDK } from "resend";
// import { vEmailId, vEmailEvent, Resend  } from "@convex-dev/resend";

const resendSDK = new ResendSDK(process.env.AUTH_RESEND_KEY);
// Create the Auth.js Resend provider for password reset
export const ResendOTPPasswordReset = Resend({
  id: "resend-otp",
  apiKey: process.env.AUTH_RESEND_KEY,
  async generateVerificationToken() {
    const random: RandomReader = {
      read(bytes) {
        crypto.getRandomValues(bytes);
      },
    };

    const alphabet = "**********";
    const length = 8;
    return generateRandomString(random, alphabet, length);
  },
  async sendVerificationRequest({ identifier: email, token, provider }) {
    // Use your Convex internal mutation to send the email
    // await ctx.runMutation(internal.emails.sendResetCodeEmail, { email, token });
    try {
      await resendSDK.emails.send({
        from: "Better Flow <<EMAIL>>",
        to: email,
        subject: "Reset your password in Better Flow",
        text: `Your password reset code is ${token}`,
        html: `<p>Your password reset code is <strong>${token}</strong></p>`,
      });
    } catch (error) {
      console.error("Failed to send reset email:", error);
      throw new Error("Could not send reset email");
    }
  },
});
