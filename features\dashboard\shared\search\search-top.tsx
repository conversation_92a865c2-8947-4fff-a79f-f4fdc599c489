import { ArrowRightIcon, SearchIcon } from 'lucide-react';

import { Input } from '@/components/ui/input';

export default function SearchTop({
  setSearchText,
  searchText,
  searchPlaceholder = 'Search..',
}: {
  setSearchText: (text: string) => void;
  searchText: string;
  searchPlaceholder?: string;
}) {
  return (
    <div className="relative flex flex-1 bg-background">
      <Input
        className="peer min-h-10 pe-0 md:ps-9 lg:pe-9 dark:bg-transparent"
        onChange={(e) => setSearchText(e.target.value)}
        placeholder={searchPlaceholder}
        type="search"
        value={searchText}
      />
      <div className="pointer-events-none absolute inset-y-0 start-0 hidden items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50 md:flex">
        <SearchIcon className="size-4" />
      </div>
      <button
        aria-label="Submit search"
        className="absolute inset-y-0 end-0 hidden h-full w-9 items-center justify-center rounded-e-md text-muted-foreground/80 outline-none transition-[color,box-shadow] hover:text-foreground focus:z-10 focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 lg:flex"
        type="submit"
      >
        <ArrowRightIcon aria-hidden="true" size={16} />
      </button>
    </div>
  );
}
