import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery } from 'convex/nextjs';
import { notFound } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import RedirectWithRole from './redirect-with-role';

export default async function Home() {
  const user = await fetchQuery(
    api.users.getUser,
    {},
    { token: await convexAuthNextjsToken() }
  );
  if (!user) {
    return notFound();
  }
  return <RedirectWithRole role={user.role ?? null} />;
}
