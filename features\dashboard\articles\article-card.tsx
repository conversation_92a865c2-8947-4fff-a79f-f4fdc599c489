import type { FunctionReturnType } from 'convex/server';
import { formatDistanceToNow } from 'date-fns';
import { ClockIcon, FileIcon } from 'lucide-react';
import Link from 'next/link';
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import type { api } from '@/convex/_generated/api';
import { getIconByStatus } from '@/features/dashboard/shared/icons';
import { calculateReadingTimeFromWords, cn } from '@/lib/utils';
import { ArticleCardActions } from './article-card-actions';

type TArticle = FunctionReturnType<typeof api.articles.getArticle>;

export default function ItemCard({ article }: { article: TArticle }) {
  if (!article || 'success' in article) {
    return null;
  }
  const Icon = getIconByStatus(article.status);

  return (
    <Card
      className={cn(
        'w-full rounded-sm shadow-xs ring-ring/20 hover:ring dark:bg-background dark:ring-ring/40',
        article.status === 'deleted' &&
          'ring-1 ring-[#f49ca2ca] dark:ring-[#551a1e]'
      )}
      key={article._id}
    >
      <CardHeader>
        <CardTitle className="mr-7 flex items-center gap-2 truncate">
          <FileIcon className="size-4 shrink-0 text-muted-foreground" />
          <span className="truncate">{article.title}</span>
        </CardTitle>
        <CardDescription className="mr-10 truncate">
          <Link
            className="hover:underline"
            href={`/author/editor/${article._id}`}
          >
            {article.slug}.better-flow.com
          </Link>
        </CardDescription>
        <CardAction>
          <ArticleCardActions article={article} />
        </CardAction>
      </CardHeader>
      <Link href="/author/editor">
        <CardContent className="flex flex-col gap-2">
          <div className="flex items-center gap-1">
            <Icon className="size-4 text-muted-foreground" />
            <p className="font-medium text-sm capitalize tracking-tight">
              {article.status}
            </p>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <ClockIcon className="size-4 text-muted-foreground" />
              <p className="text-muted-foreground text-sm">
                {calculateReadingTimeFromWords(article.words || 0)}
              </p>
            </div>
            <p className="text-muted-foreground text-sm">
              {formatDistanceToNow(new Date(article._creationTime), {
                addSuffix: true,
              })}
            </p>
          </div>
        </CardContent>
      </Link>
    </Card>
  );
}
