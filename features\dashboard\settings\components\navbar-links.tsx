'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { buttonVariants } from '@/components/ui/button';
import { settingLinks } from '@/config/dashboard';
import { cn } from '@/lib/utils';
import SearchInput from './search-input';

export function SettingLinksNavbar() {
  const pathname = usePathname();
  const basePath = `/${pathname.split('/')[1]}`;

  return (
    <div className="mr-12 hidden w-full max-w-3xs lg:block">
      <div className="sticky top-16">
        <SearchInput />
        {/* <SearchCommandInput /> */}
        <div className="flex flex-col">
          {settingLinks.map((item) => {
            const fullHref = `${basePath}${item.href}`;
            return (
              <Link
                className={cn(
                  `${buttonVariants({ variant: 'ghost', size: 'lg' })}`,
                  'justify-start rounded-md px-4'
                )}
                href={fullHref}
                key={item.label}
              >
                <span
                  className={cn(
                    'font-normal text-muted-foreground text-sm',
                    pathname.endsWith(item.href) && 'font-medium text-primary'
                  )}
                >
                  {item.label}
                </span>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}
