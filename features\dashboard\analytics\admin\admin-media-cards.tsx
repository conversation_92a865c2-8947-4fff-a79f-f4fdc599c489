'use client';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import AnalyticsCards from '@/features/dashboard/analytics/analytic-cards';
import type { TPageCard } from '@/features/dashboard/analytics/types';
import { getIconByStatus } from '@/features/dashboard/shared/icons';

export default function AdminMediaAnalyticsCards() {
  const totalStagedMedia = useQuery(api.admin.getTotalMediaFilesByStatus, {
    status: 'staged',
  });
  const totalApprovedMedia = useQuery(api.admin.getTotalMediaFilesByStatus, {
    status: 'approved',
  });
  const totalPublishedMedia = useQuery(api.admin.getTotalMediaFilesByStatus, {
    status: 'published',
  });
  const totalDeletedMedia = useQuery(api.admin.getTotalMediaFilesByStatus, {
    status: 'deleted',
  });
  const isLoading =
    totalStagedMedia === undefined ||
    totalApprovedMedia === undefined ||
    totalPublishedMedia === undefined ||
    totalDeletedMedia === undefined;
  if (isLoading) {
    return <div>Loading analytics...</div>;
  }
  const cards: TPageCard[] = [
    {
      title: 'Staged Media',
      value: totalStagedMedia,
      badgeText: 'All',
      icon: getIconByStatus('staged'),
      description: 'Visible to administrators',
      footer: 'Awaiting admin review',
    },
    {
      title: 'Approved Media',
      value: totalApprovedMedia,
      badgeText: 'All',
      icon: getIconByStatus('approved'),
      description: 'Approved media by admin',
      footer: 'Visible to the staff',
    },
    {
      title: 'Published Media',
      value: totalPublishedMedia,
      badgeText: 'All',
      icon: getIconByStatus('published'),
      description: 'Published media',
      footer: 'Visible to the public',
    },
    {
      title: 'Deleted Media',
      value: totalDeletedMedia,
      badgeText: 'All',
      icon: getIconByStatus('deleted'),
      description: 'Marked for removal by author',
      footer: 'Deleted media',
    },
  ];
  return <AnalyticsCards cards={cards} />;
}
