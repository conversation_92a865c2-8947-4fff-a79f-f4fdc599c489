'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { formatDistanceToNow } from 'date-fns';
import { ClockIcon, StarIcon } from 'lucide-react';
import Link from 'next/link';
import { DataTableColumnHeader } from '@/components/custom/data-table-column-header';
import { DragHandle } from '@/components/custom/drag-handle';
import { getIconByStatus } from '@/features/dashboard/shared/icons';
import {
  calculateReadingTimeFromWords,
  capitalize,
  truncate,
} from '@/lib/utils';
import type { TArticle } from './article-table';
import ArticleTableColumnActions from './column-actions';

export const articlesColumns: ColumnDef<TArticle>[] = [
  {
    id: 'dragHandle',
    header: '',
    cell: () => <DragHandle />,
    meta: {
      label: 'Drag Handle',
    },
  },
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => {
      const isFavorite =
        'isFavorite' in row.original && row.original.isFavorite === true;

      const articleName = (row.getValue('title') as string) ?? '';

      return (
        <Link
          className="flex items-center gap-4 font-medium decoration-dashed underline-offset-4 hover:underline"
          href={'/admin/articles'}
        >
          {truncate(capitalize(articleName), 40)}
          {isFavorite && <StarIcon className="size-4 text-muted-foreground" />}
        </Link>
      );
    },
    meta: {
      label: 'Title',
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      const Icon = getIconByStatus(status as string);
      return (
        <div className="flex items-center gap-1">
          <Icon className="size-4 text-muted-foreground" />
          <p className="font-medium text-sm capitalize tracking-tight">
            {status}
          </p>
        </div>
      );
    },
    meta: {
      label: 'Status',
    },
  },
  {
    accessorKey: 'words',
    header: 'Reading Time',
    cell: ({ row }) => {
      const wordCount = Number(row.getValue('words')) || 0;

      return (
        <div className="flex items-center gap-1">
          <ClockIcon className="size-4 text-muted-foreground" />
          <p className="text-muted-foreground text-sm">
            {calculateReadingTimeFromWords(wordCount)}
          </p>
        </div>
      );
    },
    meta: {
      label: 'Reading Time',
    },
  },
  {
    accessorKey: '_creationTime',
    header: 'Created At',
    cell: ({ row }) => {
      return (
        <span className="text-muted-foreground">
          {formatDistanceToNow(new Date(row.getValue('_creationTime')), {
            addSuffix: true,
          })}
        </span>
      );
    },
    meta: {
      label: 'Created At',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => <ArticleTableColumnActions row={row} />,
    meta: {
      label: 'Actions',
    },
  },
];
