'use client';
import { useQuery } from 'convex/react';
import { BellIcon } from 'lucide-react';
import { TabsView } from '@/components/custom/tabs-view';
import { But<PERSON> } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { api } from '@/convex/_generated/api';
import { cn } from '@/lib/utils';
import { ActivityTab } from './activity-tab';
import { InboxTab } from './inbox-tab';

export function ChatNotifications() {
  const conversations = useQuery(api.chat.getConversations);
  if (!conversations) {
    return null;
  }
  const unreadConversations = conversations.filter(
    (conversation) => conversation.unreadCount > 0
  );
  const tabs = [
    {
      value: 'inbox',
      label: 'Inbox',
      component: <InboxTab unreadConversations={unreadConversations} />,
    },

    {
      value: 'activity',
      label: 'Activity',
      component: <ActivityTab />,
    },
  ];

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className="relative size-8 rounded-full bg-transparent"
          size={'icon'}
          variant="outline"
        >
          <BellIcon className="size-5" />
          <span className="sr-only">Open chat</span>
          {unreadConversations.length > 0 && (
            <div className="absolute top-0 right-0 flex size-2">
              <span
                className={cn(
                  'absolute inline-flex h-full w-full animate-ping rounded-full bg-blue-500 opacity-75'
                )}
              />
              <span
                className={cn(
                  'relative inline-flex h-2 w-2 rounded-full bg-blue-500'
                )}
              />
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="absolute right-4 mt-4 min-h-125 min-w-105 rounded-sm bg-background p-0">
        <TabsView
          className="gap-1"
          defaultValue="inbox"
          tabContentClassName=""
          tablistClassName="mt-3"
          tabs={tabs}
        />
      </PopoverContent>
    </Popover>
  );
}
